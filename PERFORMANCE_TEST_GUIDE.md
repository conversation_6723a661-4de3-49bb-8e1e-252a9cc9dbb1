# WebSocket JSON-RPC 2.0 性能测试指南

## 🎯 测试目标

本指南提供了多种方法来测试WebSocket + JSON-RPC 2.0服务端的性能，包括：
- 多连接并发测试
- 内存使用监控
- CPU性能分析
- Goroutine数量监控
- 请求响应时间统计

## 🚀 快速开始

### 1. 启动服务器

```bash
go run main.go
```

服务器将在以下端点启动：
- WebSocket端点: `ws://localhost:8080/ws`
- 测试客户端: `http://localhost:8080`
- 性能统计: `http://localhost:8080/stats`
- pprof分析: `http://localhost:8080/debug/pprof/`

### 2. 基础性能监控

使用PowerShell查看实时统计：

```powershell
# 获取当前服务器统计
Invoke-RestMethod -Uri 'http://localhost:8080/stats' | ConvertTo-Json -Depth 10

# 持续监控（每5秒刷新一次）
while ($true) {
    Clear-Host
    $stats = Invoke-RestMethod -Uri 'http://localhost:8080/stats'
    Write-Host "=== 服务器性能统计 ===" -ForegroundColor Green
    Write-Host "运行时间: $($stats.uptime)"
    Write-Host "活跃连接: $($stats.connections.active)"
    Write-Host "总请求数: $($stats.requests.total)"
    Write-Host "内存使用: $([math]::Round($stats.memory.alloc / 1024 / 1024, 2))MB"
    Write-Host "Goroutine: $($stats.memory.num_goroutine)"
    Write-Host "QPS: $($stats.requests.qps)"
    Start-Sleep -Seconds 5
}
```

## 🧪 测试方法

### 方法1: 浏览器交互式测试

1. 访问 `http://localhost:8080`
2. 点击"连接"按钮建立WebSocket连接
3. 使用快速测试按钮或手动发送JSON-RPC请求
4. 观察通信日志和服务器响应

### 方法2: 多连接压力测试

使用提供的性能测试工具：

```bash
# 编译测试工具
cd test
go mod init test
go get github.com/gorilla/websocket
go build -o performance_test.exe performance_test.go

# 运行测试
./performance_test.exe -connections=100 -duration=60s -rate=50
```

参数说明：
- `-connections`: 并发连接数
- `-duration`: 测试持续时间
- `-rate`: 每秒请求数
- `-url`: WebSocket服务器地址

### 方法3: 简单连接测试

```bash
# 编译简单测试工具
go build -o simple_test.exe simple_test.go

# 运行测试
./simple_test.exe -connections=20 -requests=50 -method=calculator.add
```

## 📊 性能指标

### 关键指标

1. **连接性能**
   - 最大并发连接数
   - 连接建立时间
   - 连接稳定性

2. **请求性能**
   - QPS (每秒查询数)
   - 平均响应时间
   - 请求成功率

3. **内存性能**
   - 内存使用量
   - 堆内存分配
   - GC频率和耗时

4. **CPU性能**
   - CPU使用率
   - Goroutine数量
   - 系统调用次数

### 性能基准

基于测试环境的典型性能指标：

| 指标 | 轻负载 | 中负载 | 重负载 |
|------|--------|--------|--------|
| 并发连接 | 1-50 | 50-200 | 200+ |
| QPS | <100 | 100-500 | 500+ |
| 内存使用 | <10MB | 10-50MB | 50MB+ |
| 响应时间 | <10ms | 10-50ms | 50ms+ |

## 🔧 性能分析工具

### 1. 内置统计端点

```bash
# 获取JSON格式统计
curl http://localhost:8080/stats

# 打印控制台统计
curl http://localhost:8080/stats/print
```

### 2. Go pprof工具

```bash
# CPU性能分析
go tool pprof http://localhost:8080/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:8080/debug/pprof/heap

# Goroutine分析
go tool pprof http://localhost:8080/debug/pprof/goroutine

# 阻塞分析
go tool pprof http://localhost:8080/debug/pprof/block
```

### 3. 实时监控

访问以下URL进行实时监控：
- http://localhost:8080/debug/pprof/ - pprof主页
- http://localhost:8080/debug/pprof/goroutine?debug=1 - Goroutine详情
- http://localhost:8080/debug/pprof/heap?debug=1 - 内存详情

## 📈 测试场景

### 场景1: 基础功能测试
- 连接数: 10
- 请求数: 100
- 目标: 验证基本功能正常

### 场景2: 中等负载测试
- 连接数: 100
- 持续时间: 5分钟
- 请求速率: 50 req/s
- 目标: 测试稳定性

### 场景3: 高负载压力测试
- 连接数: 500-1000
- 持续时间: 10分钟
- 请求速率: 200+ req/s
- 目标: 找到性能瓶颈

### 场景4: 长时间稳定性测试
- 连接数: 200
- 持续时间: 1小时+
- 请求速率: 100 req/s
- 目标: 验证长期稳定性

## 🐛 常见问题

### 1. 连接失败
- 检查服务器是否启动
- 确认端口8080未被占用
- 检查防火墙设置

### 2. 内存持续增长
- 检查是否有内存泄漏
- 观察GC频率
- 使用pprof分析内存分配

### 3. 响应时间过长
- 检查CPU使用率
- 分析Goroutine数量
- 优化RPC方法实现

### 4. 连接断开
- 检查网络稳定性
- 调整心跳间隔
- 增加错误重试机制

## 📝 测试报告模板

```
# 性能测试报告

## 测试环境
- 操作系统: Windows/Linux/macOS
- Go版本: 1.21+
- CPU: 核心数
- 内存: 总内存

## 测试配置
- 并发连接数: X
- 测试持续时间: X分钟
- 请求速率: X req/s

## 测试结果
- 总请求数: X
- 成功请求: X
- 失败请求: X
- 平均响应时间: X ms
- QPS: X
- 内存峰值: X MB
- Goroutine峰值: X

## 性能分析
- CPU使用率: X%
- 内存使用趋势: 稳定/增长/波动
- GC频率: X次/分钟
- 瓶颈分析: ...

## 结论和建议
- 性能表现: 优秀/良好/需要优化
- 改进建议: ...
```

## 🎯 优化建议

1. **连接池管理**: 实现连接复用和池化
2. **消息批处理**: 支持批量请求处理
3. **内存优化**: 减少不必要的内存分配
4. **并发控制**: 限制最大并发连接数
5. **监控告警**: 添加性能指标告警机制

---

通过以上测试方法和工具，你可以全面评估WebSocket JSON-RPC服务的性能表现，并根据测试结果进行针对性优化。
