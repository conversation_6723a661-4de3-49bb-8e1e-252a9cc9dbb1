package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// Stats 统计信息
type Stats struct {
	ConnectedClients int64
	TotalRequests    int64
	TotalResponses   int64
	TotalErrors      int64
	StartTime        time.Time
}

// JSONRPCRequest JSON-RPC请求
type JSONRPCRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
	ID      int         `json:"id"`
}

func main() {
	var (
		serverURL   = flag.String("url", "ws://localhost:8080/ws", "WebSocket服务器地址")
		connections = flag.Int("connections", 1000, "并发连接数")
		duration    = flag.Duration("duration", 60*time.Second, "测试持续时间")
		requestRate = flag.Int("rate", 10, "每个连接每秒请求数")
	)
	flag.Parse()

	fmt.Printf("=== 高负载性能测试 ===\n")
	fmt.Printf("服务器地址: %s\n", *serverURL)
	fmt.Printf("并发连接数: %d\n", *connections)
	fmt.Printf("测试持续时间: %v\n", *duration)
	fmt.Printf("每连接请求速率: %d req/s\n", *requestRate)
	fmt.Printf("预计总请求数: %d\n", *connections * *requestRate * int(duration.Seconds()))

	stats := &Stats{
		StartTime: time.Now(),
	}

	// 启动统计监控
	go printStats(stats, *duration)

	var wg sync.WaitGroup
	
	fmt.Printf("\n开始创建 %d 个连接...\n", *connections)
	
	// 分批创建连接，避免一次性创建太多连接导致系统负载过高
	batchSize := 50
	for i := 0; i < *connections; i += batchSize {
		end := i + batchSize
		if end > *connections {
			end = *connections
		}
		
		// 创建一批连接
		for j := i; j < end; j++ {
			wg.Add(1)
			go func(connID int) {
				defer wg.Done()
				runClient(connID, *serverURL, *duration, *requestRate, stats)
			}(j)
			
			// 每个连接之间间隔10ms，避免连接风暴
			time.Sleep(10 * time.Millisecond)
		}
		
		fmt.Printf("已启动 %d/%d 连接\n", end, *connections)
		time.Sleep(100 * time.Millisecond) // 批次间隔
	}

	// 等待所有连接完成
	wg.Wait()

	// 打印最终结果
	printFinalStats(stats)
}

func runClient(connID int, serverURL string, duration time.Duration, requestRate int, stats *Stats) {
	u, err := url.Parse(serverURL)
	if err != nil {
		atomic.AddInt64(&stats.TotalErrors, 1)
		return
	}

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		atomic.AddInt64(&stats.TotalErrors, 1)
		return
	}
	defer conn.Close()

	atomic.AddInt64(&stats.ConnectedClients, 1)

	// 启动响应读取协程
	go func() {
		for {
			_, _, err := conn.ReadMessage()
			if err != nil {
				return
			}
			atomic.AddInt64(&stats.TotalResponses, 1)
		}
	}()

	// 发送请求
	ticker := time.NewTicker(time.Second / time.Duration(requestRate))
	defer ticker.Stop()

	timeout := time.After(duration)
	requestID := 1

	for {
		select {
		case <-timeout:
			return
		case <-ticker.C:
			req := JSONRPCRequest{
				JSONRPC: "2.0",
				Method:  "calculator.add",
				Params:  map[string]float64{"a": 10.5, "b": 5.3},
				ID:      requestID,
			}
			requestID++

			if err := conn.WriteJSON(req); err != nil {
				atomic.AddInt64(&stats.TotalErrors, 1)
				return
			}
			atomic.AddInt64(&stats.TotalRequests, 1)
		}
	}
}

func printStats(stats *Stats, duration time.Duration) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			elapsed := time.Since(stats.StartTime)
			connected := atomic.LoadInt64(&stats.ConnectedClients)
			requests := atomic.LoadInt64(&stats.TotalRequests)
			responses := atomic.LoadInt64(&stats.TotalResponses)
			errors := atomic.LoadInt64(&stats.TotalErrors)

			fmt.Printf("\n--- 实时统计 (运行时间: %v) ---\n", elapsed.Round(time.Second))
			fmt.Printf("已连接客户端: %d\n", connected)
			fmt.Printf("发送请求: %d\n", requests)
			fmt.Printf("收到响应: %d\n", responses)
			fmt.Printf("错误数量: %d\n", errors)

			if elapsed.Seconds() > 0 {
				qps := float64(requests) / elapsed.Seconds()
				fmt.Printf("当前QPS: %.2f\n", qps)
			}

			if requests > 0 {
				successRate := float64(responses) / float64(requests) * 100
				fmt.Printf("成功率: %.2f%%\n", successRate)
			}

		case <-time.After(duration + 10*time.Second):
			return
		}
	}
}

func printFinalStats(stats *Stats) {
	duration := time.Since(stats.StartTime)
	connected := atomic.LoadInt64(&stats.ConnectedClients)
	requests := atomic.LoadInt64(&stats.TotalRequests)
	responses := atomic.LoadInt64(&stats.TotalResponses)
	errors := atomic.LoadInt64(&stats.TotalErrors)

	fmt.Printf("\n=== 最终测试结果 ===\n")
	fmt.Printf("测试持续时间: %v\n", duration.Round(time.Second))
	fmt.Printf("成功连接数: %d\n", connected)
	fmt.Printf("总发送请求: %d\n", requests)
	fmt.Printf("总收到响应: %d\n", responses)
	fmt.Printf("总错误数: %d\n", errors)

	if duration.Seconds() > 0 {
		avgQPS := float64(requests) / duration.Seconds()
		fmt.Printf("平均QPS: %.2f\n", avgQPS)
	}

	if requests > 0 {
		successRate := float64(responses) / float64(requests) * 100
		fmt.Printf("总体成功率: %.2f%%\n", successRate)
	}

	if connected > 0 {
		avgRequestsPerConn := float64(requests) / float64(connected)
		fmt.Printf("每连接平均请求数: %.2f\n", avgRequestsPerConn)
	}

	fmt.Printf("\n测试完成！\n")
}
