package main

import (
	"context"
	"log"
	"net/http"
	_ "net/http/pprof" // 导入pprof
	"time"

	"golang-ws-jsonrpc/jsonrpc"
	"golang-ws-jsonrpc/monitor"
	"golang-ws-jsonrpc/services"
	"golang-ws-jsonrpc/websocket"
)

func main() {
	// 创建性能监控器
	perfMonitor := monitor.NewPerformanceMonitor()
	ctx := context.Background()
	perfMonitor.Start(ctx, 5*time.Second) // 每5秒收集一次统计信息

	// 创建JSON-RPC服务器
	rpcServer := jsonrpc.NewServer()

	// 创建并注册服务
	calculatorService := services.NewCalculatorService()
	utilityService := services.NewUtilityService()

	// 注册计算器服务方法
	if err := rpcServer.RegisterFunc("calculator.add", calculatorService.Add); err != nil {
		log.Fatalf("Failed to register calculator.add: %v", err)
	}
	if err := rpcServer.RegisterFunc("calculator.subtract", calculatorService.Subtract); err != nil {
		log.Fatalf("Failed to register calculator.subtract: %v", err)
	}
	if err := rpcServer.RegisterFunc("calculator.multiply", calculatorService.Multiply); err != nil {
		log.Fatalf("Failed to register calculator.multiply: %v", err)
	}
	if err := rpcServer.RegisterFunc("calculator.divide", calculatorService.Divide); err != nil {
		log.Fatalf("Failed to register calculator.divide: %v", err)
	}

	// 注册工具服务方法
	if err := rpcServer.RegisterFunc("utility.ping", utilityService.Ping); err != nil {
		log.Fatalf("Failed to register utility.ping: %v", err)
	}
	if err := rpcServer.RegisterFunc("utility.echo", utilityService.Echo); err != nil {
		log.Fatalf("Failed to register utility.echo: %v", err)
	}
	if err := rpcServer.RegisterFunc("utility.getTime", utilityService.GetTime); err != nil {
		log.Fatalf("Failed to register utility.getTime: %v", err)
	}
	if err := rpcServer.RegisterFunc("utility.sleep", utilityService.Sleep); err != nil {
		log.Fatalf("Failed to register utility.sleep: %v", err)
	}

	// 创建WebSocket服务器
	wsServer := websocket.NewServer(rpcServer)

	// 将性能监控器传递给WebSocket服务器
	wsServer.SetPerformanceMonitor(perfMonitor)

	// 启动WebSocket服务器协程
	go wsServer.Run()

	// 设置HTTP路由
	http.HandleFunc("/ws", wsServer.HandleWebSocket)
	http.Handle("/", http.FileServer(http.Dir("static/")))

	// 添加性能监控端点
	http.HandleFunc("/stats", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		data, err := perfMonitor.ExportJSON()
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		w.Write(data)
	})

	// 添加性能统计打印端点
	http.HandleFunc("/stats/print", func(w http.ResponseWriter, r *http.Request) {
		perfMonitor.PrintStats()
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Stats printed to console"))
	})

	// 打印注册的方法
	methods := rpcServer.ListMethods()
	log.Printf("Registered RPC methods: %v", methods)

	// 启动HTTP服务器
	log.Println("WebSocket JSON-RPC server starting on :8080")
	log.Println("WebSocket endpoint: ws://localhost:8080/ws")
	log.Println("Test client: http://localhost:8080")

	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}
