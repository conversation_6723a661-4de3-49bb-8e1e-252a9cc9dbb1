package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// SimpleStats 简单统计
type SimpleStats struct {
	Connections   int64
	TotalRequests int64
	Responses     int64
	Errors        int64
	StartTime     time.Time
}

// JSONRPCRequest JSON-RPC请求
type JSONRPCRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
	ID      int         `json:"id"`
}

func main() {
	var (
		serverURL   = flag.String("url", "ws://localhost:8080/ws", "WebSocket服务器地址")
		connections = flag.Int("connections", 10, "并发连接数")
		requests    = flag.Int("requests", 100, "每个连接发送的请求数")
		method      = flag.String("method", "calculator.add", "测试的RPC方法")
	)
	flag.Parse()

	fmt.Printf("=== 简单性能测试 ===\n")
	fmt.Printf("服务器: %s\n", *serverURL)
	fmt.Printf("连接数: %d\n", *connections)
	fmt.Printf("每连接请求数: %d\n", *requests)
	fmt.Printf("测试方法: %s\n", *method)
	fmt.Printf("总请求数: %d\n", (*connections)*(*requests))

	stats := &SimpleStats{
		StartTime: time.Now(),
	}

	var wg sync.WaitGroup
	
	// 启动多个连接
	for i := 0; i < *connections; i++ {
		wg.Add(1)
		go func(connID int) {
			defer wg.Done()
			runConnection(connID, *serverURL, *requests, *method, stats)
		}(i)
	}

	// 等待所有连接完成
	wg.Wait()

	// 打印结果
	duration := time.Since(stats.StartTime)
	fmt.Printf("\n=== 测试结果 ===\n")
	fmt.Printf("测试时间: %v\n", duration)
	fmt.Printf("成功连接: %d\n", stats.Connections)
	fmt.Printf("发送请求: %d\n", stats.TotalRequests)
	fmt.Printf("收到响应: %d\n", stats.Responses)
	fmt.Printf("错误数量: %d\n", stats.Errors)
	
	if stats.TotalRequests > 0 {
		successRate := float64(stats.Responses) / float64(stats.TotalRequests) * 100
		fmt.Printf("成功率: %.2f%%\n", successRate)
	}
	
	if duration.Seconds() > 0 {
		qps := float64(stats.TotalRequests) / duration.Seconds()
		fmt.Printf("QPS: %.2f\n", qps)
	}
}

func runConnection(connID int, serverURL string, requestCount int, method string, stats *SimpleStats) {
	u, err := url.Parse(serverURL)
	if err != nil {
		log.Printf("连接 %d: URL解析错误: %v", connID, err)
		atomic.AddInt64(&stats.Errors, 1)
		return
	}

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Printf("连接 %d: 连接失败: %v", connID, err)
		atomic.AddInt64(&stats.Errors, 1)
		return
	}
	defer conn.Close()

	atomic.AddInt64(&stats.Connections, 1)

	// 启动响应读取协程
	responseChan := make(chan bool, requestCount)
	go func() {
		for {
			_, _, err := conn.ReadMessage()
			if err != nil {
				return
			}
			atomic.AddInt64(&stats.Responses, 1)
			responseChan <- true
		}
	}()

	// 发送请求
	for i := 0; i < requestCount; i++ {
		req := createRequest(method, i+1)
		
		if err := conn.WriteJSON(req); err != nil {
			log.Printf("连接 %d: 发送请求失败: %v", connID, err)
			atomic.AddInt64(&stats.Errors, 1)
			continue
		}
		
		atomic.AddInt64(&stats.TotalRequests, 1)
		
		// 简单的速率控制
		time.Sleep(10 * time.Millisecond)
	}

	// 等待响应（最多等待5秒）
	timeout := time.After(5 * time.Second)
	receivedResponses := 0
	
	for receivedResponses < requestCount {
		select {
		case <-responseChan:
			receivedResponses++
		case <-timeout:
			log.Printf("连接 %d: 响应超时，收到 %d/%d 响应", connID, receivedResponses, requestCount)
			return
		}
	}
}

func createRequest(method string, id int) JSONRPCRequest {
	var params interface{}

	switch method {
	case "calculator.add":
		params = map[string]float64{"a": 10.0, "b": 5.0}
	case "calculator.multiply":
		params = map[string]float64{"a": 3.0, "b": 4.0}
	case "calculator.divide":
		params = map[string]float64{"a": 20.0, "b": 4.0}
	case "utility.echo":
		params = map[string]string{"message": fmt.Sprintf("test %d", id)}
	case "utility.ping":
		params = nil
	case "utility.getTime":
		params = nil
	default:
		params = nil
	}

	return JSONRPCRequest{
		JSONRPC: "2.0",
		Method:  method,
		Params:  params,
		ID:      id,
	}
}
