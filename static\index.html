<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket JSON-RPC 2.0 测试客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .request {
            color: #007bff;
        }
        .response {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .quick-test {
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }
        .quick-test:hover {
            background-color: #dee2e6;
        }
    </style>
</head>
<body>
    <h1>WebSocket JSON-RPC 2.0 测试客户端</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/ws" placeholder="ws://localhost:8080/ws">
        </div>
        
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="container">
        <h3>发送JSON-RPC请求</h3>
        
        <div class="form-group">
            <label for="method">方法名:</label>
            <input type="text" id="method" placeholder="calculator.add" value="calculator.add">
        </div>
        
        <div class="form-group">
            <label for="params">参数 (JSON格式):</label>
            <textarea id="params" rows="3" placeholder='{"a": 10, "b": 5}'>{"a": 10, "b": 5}</textarea>
        </div>
        
        <div class="form-group">
            <label for="requestId">请求ID (留空为通知):</label>
            <input type="text" id="requestId" placeholder="1" value="1">
        </div>
        
        <button onclick="sendRequest()" id="sendBtn" disabled>发送请求</button>
        
        <div class="quick-tests">
            <div class="quick-test" onclick="quickTest('ping')">Ping测试</div>
            <div class="quick-test" onclick="quickTest('add')">加法测试</div>
            <div class="quick-test" onclick="quickTest('divide')">除法测试</div>
            <div class="quick-test" onclick="quickTest('echo')">Echo测试</div>
            <div class="quick-test" onclick="quickTest('time')">获取时间</div>
            <div class="quick-test" onclick="quickTest('sleep')">睡眠测试</div>
        </div>
    </div>

    <div class="container">
        <h3>通信日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let requestId = 1;

        function connect() {
            const url = document.getElementById('serverUrl').value;
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function() {
                    updateStatus(true);
                    log('连接已建立', 'info');
                };
                
                ws.onmessage = function(event) {
                    log('← ' + event.data, 'response');
                };
                
                ws.onclose = function() {
                    updateStatus(false);
                    log('连接已关闭', 'info');
                };
                
                ws.onerror = function(error) {
                    log('连接错误: ' + error, 'error');
                };
                
            } catch (error) {
                log('连接失败: ' + error.message, 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function updateStatus(connected) {
            const status = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            
            if (connected) {
                status.textContent = '已连接';
                status.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
            } else {
                status.textContent = '未连接';
                status.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
            }
        }

        function sendRequest() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接', 'error');
                return;
            }

            const method = document.getElementById('method').value;
            const paramsText = document.getElementById('params').value;
            const id = document.getElementById('requestId').value;

            let params = null;
            if (paramsText.trim()) {
                try {
                    params = JSON.parse(paramsText);
                } catch (error) {
                    log('参数JSON格式错误: ' + error.message, 'error');
                    return;
                }
            }

            const request = {
                jsonrpc: '2.0',
                method: method,
                params: params,
                id: id || null
            };

            const requestStr = JSON.stringify(request);
            ws.send(requestStr);
            log('→ ' + requestStr, 'request');
        }

        function quickTest(type) {
            const tests = {
                ping: {
                    method: 'utility.ping',
                    params: null,
                    id: requestId++
                },
                add: {
                    method: 'calculator.add',
                    params: {a: 10, b: 5},
                    id: requestId++
                },
                divide: {
                    method: 'calculator.divide',
                    params: {a: 10, b: 0},
                    id: requestId++
                },
                echo: {
                    method: 'utility.echo',
                    params: {message: 'Hello WebSocket JSON-RPC!'},
                    id: requestId++
                },
                time: {
                    method: 'utility.getTime',
                    params: null,
                    id: requestId++
                },
                sleep: {
                    method: 'utility.sleep',
                    params: {seconds: 2},
                    id: requestId++
                }
            };

            const test = tests[type];
            if (test) {
                document.getElementById('method').value = test.method;
                document.getElementById('params').value = test.params ? JSON.stringify(test.params, null, 2) : '';
                document.getElementById('requestId').value = test.id;
            }
        }

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
    </script>
</body>
</html>
