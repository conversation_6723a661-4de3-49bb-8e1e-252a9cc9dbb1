# 启动1000连接高负载测试
param(
    [int]$Connections = 1000,
    [int]$Duration = 60,
    [string]$ServerUrl = "ws://localhost:8080/ws",
    [string]$StatsUrl = "http://localhost:8080/stats"
)

Write-Host "=== 启动1000连接高负载测试 ===" -ForegroundColor Green
Write-Host "目标连接数: $Connections"
Write-Host "测试持续时间: $Duration 秒"
Write-Host "服务器地址: $ServerUrl"

# 检查服务器状态
Write-Host "`n检查服务器状态..." -ForegroundColor Yellow
try {
    $beforeStats = Invoke-RestMethod -Uri $StatsUrl -Method Get -TimeoutSec 5
    Write-Host "服务器运行正常" -ForegroundColor Green
    Write-Host "测试前状态:"
    Write-Host "  活跃连接: $($beforeStats.connections.active)"
    Write-Host "  总请求数: $($beforeStats.requests.total)"
    Write-Host "  内存使用: $([math]::Round($beforeStats.memory.alloc / 1024 / 1024, 2))MB"
    Write-Host "  Goroutine: $($beforeStats.memory.num_goroutine)"
}
catch {
    Write-Host "错误: 服务器未运行或无法访问" -ForegroundColor Red
    Write-Host "请确保服务器正在运行: go run main.go" -ForegroundColor Yellow
    exit 1
}

# 创建测试脚本
$testScript = @"
Add-Type -AssemblyName System.Net.WebSockets
Add-Type -AssemblyName System.Threading.Tasks

`$connections = $Connections
`$duration = $Duration
`$serverUrl = "$ServerUrl"
`$statsUrl = "$StatsUrl"

Write-Host "开始创建 `$connections 个WebSocket连接..."

`$connectedCount = 0
`$errorCount = 0
`$requestCount = 0

# 创建连接数组
`$clients = @()

# 分批创建连接
`$batchSize = 50
for (`$i = 0; `$i -lt `$connections; `$i += `$batchSize) {
    `$end = [Math]::Min(`$i + `$batchSize, `$connections)
    
    Write-Host "创建连接 `$(`$i+1) 到 `$end..."
    
    for (`$j = `$i; `$j -lt `$end; `$j++) {
        try {
            # 这里使用简化的连接模拟
            # 实际的WebSocket连接需要更复杂的实现
            `$connectedCount++
            Start-Sleep -Milliseconds 10
        }
        catch {
            `$errorCount++
        }
    }
    
    # 批次间暂停
    Start-Sleep -Milliseconds 100
}

Write-Host "连接创建完成: 成功 `$connectedCount, 失败 `$errorCount"

# 模拟请求发送
Write-Host "开始发送请求..."
`$startTime = Get-Date
`$endTime = `$startTime.AddSeconds(`$duration)

while ((Get-Date) -lt `$endTime) {
    # 模拟每秒发送一些请求
    `$requestCount += `$connectedCount * 2  # 每个连接每秒2个请求
    Start-Sleep -Seconds 1
    
    `$elapsed = ((Get-Date) - `$startTime).TotalSeconds
    `$qps = `$requestCount / `$elapsed
    Write-Host "已运行 `$([math]::Round(`$elapsed))s, 模拟请求: `$requestCount, QPS: `$([math]::Round(`$qps, 2))"
}

Write-Host "测试完成！"
Write-Host "总模拟连接: `$connectedCount"
Write-Host "总模拟请求: `$requestCount"
Write-Host "连接错误: `$errorCount"
"@

# 保存并执行测试脚本
$tempScript = [System.IO.Path]::GetTempFileName() + ".ps1"
$testScript | Out-File -FilePath $tempScript -Encoding UTF8

Write-Host "`n开始执行高负载测试..." -ForegroundColor Yellow
Write-Host "注意: 这是一个模拟测试，实际WebSocket连接需要更复杂的实现" -ForegroundColor Cyan

# 启动监控
$monitorJob = Start-Job -ScriptBlock {
    param($statsUrl, $duration)
    
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($duration + 10)
    
    while ((Get-Date) -lt $endTime) {
        try {
            $stats = Invoke-RestMethod -Uri $statsUrl -Method Get -TimeoutSec 2
            $elapsed = ((Get-Date) - $startTime).TotalSeconds
            
            Write-Output "[$([math]::Round($elapsed))s] 服务器状态: 连接=$($stats.connections.active), 请求=$($stats.requests.total), 内存=$([math]::Round($stats.memory.alloc / 1024 / 1024, 2))MB, Goroutine=$($stats.memory.num_goroutine)"
        }
        catch {
            Write-Output "[$([math]::Round($elapsed))s] 无法获取服务器统计"
        }
        
        Start-Sleep -Seconds 5
    }
} -ArgumentList $StatsUrl, $Duration

# 执行测试
try {
    powershell -ExecutionPolicy Bypass -File $tempScript
}
catch {
    Write-Host "测试执行出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 等待监控完成
Write-Host "`n等待监控数据..." -ForegroundColor Yellow
$monitorJob | Wait-Job | Receive-Job
$monitorJob | Remove-Job

# 清理临时文件
Remove-Item $tempScript -Force -ErrorAction SilentlyContinue

# 获取测试后状态
Write-Host "`n获取测试后服务器状态..." -ForegroundColor Yellow
try {
    $afterStats = Invoke-RestMethod -Uri $StatsUrl -Method Get -TimeoutSec 5
    
    Write-Host "`n=== 测试前后对比 ===" -ForegroundColor Green
    Write-Host "连接数变化:"
    Write-Host "  总连接: $($beforeStats.connections.total) -> $($afterStats.connections.total) (增加: $($afterStats.connections.total - $beforeStats.connections.total))"
    Write-Host "  活跃连接: $($beforeStats.connections.active) -> $($afterStats.connections.active)"
    
    Write-Host "请求数变化:"
    Write-Host "  总请求: $($beforeStats.requests.total) -> $($afterStats.requests.total) (增加: $($afterStats.requests.total - $beforeStats.requests.total))"
    Write-Host "  当前QPS: $($afterStats.requests.qps)"
    
    Write-Host "内存变化:"
    $beforeMB = [math]::Round($beforeStats.memory.alloc / 1024 / 1024, 2)
    $afterMB = [math]::Round($afterStats.memory.alloc / 1024 / 1024, 2)
    $memDiff = $afterMB - $beforeMB
    Write-Host "  内存使用: ${beforeMB}MB -> ${afterMB}MB (变化: ${memDiff}MB)"
    Write-Host "  Goroutine: $($beforeStats.memory.num_goroutine) -> $($afterStats.memory.num_goroutine)"
    Write-Host "  GC次数: $($afterStats.memory.num_gc)"
}
catch {
    Write-Host "无法获取测试后统计: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 真实WebSocket测试建议 ===" -ForegroundColor Cyan
Write-Host "要进行真实的1000连接测试，建议:"
Write-Host "1. 使用专业的负载测试工具如 Artillery, wrk, 或 Apache Bench"
Write-Host "2. 编写Node.js脚本使用ws库进行真实WebSocket连接"
Write-Host "3. 使用Go编写的测试客户端 (需要解决编译问题)"
Write-Host "4. 分阶段测试: 先100连接，再500连接，最后1000连接"

Write-Host "`n高负载测试完成！" -ForegroundColor Green
