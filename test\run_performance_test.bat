@echo off
setlocal enabledelayedexpansion

echo === WebSocket JSON-RPC 性能测试 ===

REM 配置参数
set SERVER_URL=ws://localhost:8080/ws
set TEST_DURATION=60s
set STATS_URL=http://localhost:8080/stats

REM 检查服务器是否运行
echo 检查服务器状态...
curl -s "%STATS_URL%" >nul 2>&1
if errorlevel 1 (
    echo 错误: 服务器未运行，请先启动服务器
    echo 运行命令: go run main.go
    pause
    exit /b 1
)

echo 服务器运行正常

REM 创建结果目录
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set RESULT_DIR=performance_results_%mydate%_%mytime%
mkdir "%RESULT_DIR%"

echo 测试结果将保存到: %RESULT_DIR%

REM 编译测试程序
echo 编译性能测试程序...
cd test
go build -o performance_test.exe performance_test.go
cd ..

REM 测试场景
set scenarios=10:5 50:10 100:20 200:50 500:100

for %%s in (%scenarios%) do (
    for /f "tokens=1,2 delims=:" %%a in ("%%s") do (
        set connections=%%a
        set rate=%%b
        
        echo.
        echo === 测试场景: !connections!个连接, !rate!req/s ===
        
        REM 获取测试前的服务器状态
        echo 获取测试前服务器状态...
        curl -s "%STATS_URL%" > "%RESULT_DIR%\before_!connections!_!rate!.json"
        
        REM 运行性能测试
        echo 开始性能测试...
        start /b "" test\performance_test.exe -url="%SERVER_URL%" -connections=!connections! -duration="%TEST_DURATION%" -rate=!rate! > "%RESULT_DIR%\test_!connections!_!rate!.log" 2>&1
        
        REM 等待测试完成（简化版，实际应该检查进程状态）
        timeout /t 65 /nobreak >nul
        
        REM 获取测试后的服务器状态
        echo 获取测试后服务器状态...
        curl -s "%STATS_URL%" > "%RESULT_DIR%\after_!connections!_!rate!.json"
        
        echo 测试场景 !connections!连接/!rate!req/s 完成
        
        REM 等待服务器恢复
        echo 等待服务器恢复...
        timeout /t 10 /nobreak >nul
    )
)

echo.
echo === 所有测试完成 ===

REM 生成简单报告
echo 生成测试报告...
set REPORT_FILE=%RESULT_DIR%\performance_report.txt

echo WebSocket JSON-RPC 性能测试报告 > "%REPORT_FILE%"
echo 测试时间: %date% %time% >> "%REPORT_FILE%"
echo 测试持续时间: %TEST_DURATION% >> "%REPORT_FILE%"
echo 服务器地址: %SERVER_URL% >> "%REPORT_FILE%"
echo. >> "%REPORT_FILE%"

for %%s in (%scenarios%) do (
    for /f "tokens=1,2 delims=:" %%a in ("%%s") do (
        set connections=%%a
        set rate=%%b
        
        echo === !connections!个连接, !rate!req/s === >> "%REPORT_FILE%"
        if exist "%RESULT_DIR%\test_!connections!_!rate!.log" (
            echo 客户端测试结果: >> "%REPORT_FILE%"
            type "%RESULT_DIR%\test_!connections!_!rate!.log" >> "%REPORT_FILE%"
        )
        echo. >> "%REPORT_FILE%"
    )
)

echo 测试报告已生成: %REPORT_FILE%

REM 打开结果目录
explorer "%RESULT_DIR%"

echo 测试完成！结果保存在: %RESULT_DIR%
pause
