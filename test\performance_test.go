package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// TestConfig 测试配置
type TestConfig struct {
	ServerURL     string
	Connections   int
	Duration      time.Duration
	RequestRate   int // 每秒请求数
	TestMethods   []string
}

// TestStats 测试统计
type TestStats struct {
	TotalRequests    int64
	TotalResponses   int64
	TotalErrors      int64
	SuccessRequests  int64
	FailedRequests   int64
	ConnectionErrors int64
	StartTime        time.Time
	EndTime          time.Time
	ResponseTimes    []time.Duration
	mu               sync.Mutex
}

// JSONRPCRequest JSON-RPC请求结构
type JSONRPCRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
	ID      int         `json:"id"`
}

// JSONRPCResponse JSON-RPC响应结构
type JSONRPCResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	Result  interface{} `json:"result,omitempty"`
	Error   interface{} `json:"error,omitempty"`
	ID      int         `json:"id"`
}

// WebSocketClient WebSocket客户端
type WebSocketClient struct {
	conn     *websocket.Conn
	id       int
	stats    *TestStats
	requests chan JSONRPCRequest
	done     chan bool
}

// NewWebSocketClient 创建新的WebSocket客户端
func NewWebSocketClient(id int, serverURL string, stats *TestStats) (*WebSocketClient, error) {
	u, err := url.Parse(serverURL)
	if err != nil {
		return nil, err
	}

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		atomic.AddInt64(&stats.ConnectionErrors, 1)
		return nil, err
	}

	client := &WebSocketClient{
		conn:     conn,
		id:       id,
		stats:    stats,
		requests: make(chan JSONRPCRequest, 100),
		done:     make(chan bool),
	}

	return client, nil
}

// Start 启动客户端
func (c *WebSocketClient) Start() {
	go c.readPump()
	go c.writePump()
}

// Stop 停止客户端
func (c *WebSocketClient) Stop() {
	close(c.done)
	c.conn.Close()
}

// SendRequest 发送请求
func (c *WebSocketClient) SendRequest(req JSONRPCRequest) {
	select {
	case c.requests <- req:
	default:
		// 请求队列满，丢弃请求
		atomic.AddInt64(&c.stats.TotalErrors, 1)
	}
}

// readPump 读取响应
func (c *WebSocketClient) readPump() {
	defer c.conn.Close()

	for {
		select {
		case <-c.done:
			return
		default:
			_, message, err := c.conn.ReadMessage()
			if err != nil {
				atomic.AddInt64(&c.stats.TotalErrors, 1)
				return
			}

			var response JSONRPCResponse
			if err := json.Unmarshal(message, &response); err != nil {
				atomic.AddInt64(&c.stats.TotalErrors, 1)
				continue
			}

			atomic.AddInt64(&c.stats.TotalResponses, 1)
			if response.Error != nil {
				atomic.AddInt64(&c.stats.FailedRequests, 1)
			} else {
				atomic.AddInt64(&c.stats.SuccessRequests, 1)
			}
		}
	}
}

// writePump 发送请求
func (c *WebSocketClient) writePump() {
	defer c.conn.Close()

	for {
		select {
		case <-c.done:
			return
		case req := <-c.requests:
			startTime := time.Now()
			
			if err := c.conn.WriteJSON(req); err != nil {
				atomic.AddInt64(&c.stats.TotalErrors, 1)
				return
			}

			atomic.AddInt64(&c.stats.TotalRequests, 1)
			
			// 记录响应时间（简化版，实际应该在收到响应时计算）
			responseTime := time.Since(startTime)
			c.stats.mu.Lock()
			c.stats.ResponseTimes = append(c.stats.ResponseTimes, responseTime)
			c.stats.mu.Unlock()
		}
	}
}

// PerformanceTest 性能测试主函数
func PerformanceTest(config TestConfig) *TestStats {
	stats := &TestStats{
		StartTime:     time.Now(),
		ResponseTimes: make([]time.Duration, 0),
	}

	// 创建客户端连接
	clients := make([]*WebSocketClient, 0, config.Connections)
	var wg sync.WaitGroup

	log.Printf("创建 %d 个WebSocket连接...", config.Connections)
	
	for i := 0; i < config.Connections; i++ {
		client, err := NewWebSocketClient(i, config.ServerURL, stats)
		if err != nil {
			log.Printf("创建客户端 %d 失败: %v", i, err)
			continue
		}
		clients = append(clients, client)
		client.Start()
	}

	log.Printf("成功创建 %d 个连接", len(clients))

	// 开始测试
	ctx, cancel := context.WithTimeout(context.Background(), config.Duration)
	defer cancel()

	// 请求发送器
	wg.Add(1)
	go func() {
		defer wg.Done()
		ticker := time.NewTicker(time.Second / time.Duration(config.RequestRate))
		defer ticker.Stop()

		requestID := 1
		methodIndex := 0

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				// 轮询发送不同类型的请求
				method := config.TestMethods[methodIndex%len(config.TestMethods)]
				methodIndex++

				req := createTestRequest(method, requestID)
				requestID++

				// 随机选择一个客户端发送请求
				if len(clients) > 0 {
					clientIndex := requestID % len(clients)
					clients[clientIndex].SendRequest(req)
				}
			}
		}
	}()

	// 等待测试完成
	<-ctx.Done()
	
	// 停止所有客户端
	for _, client := range clients {
		client.Stop()
	}

	wg.Wait()
	stats.EndTime = time.Now()

	return stats
}

// createTestRequest 创建测试请求
func createTestRequest(method string, id int) JSONRPCRequest {
	var params interface{}

	switch method {
	case "calculator.add":
		params = map[string]float64{"a": 10.5, "b": 5.3}
	case "calculator.multiply":
		params = map[string]float64{"a": 3.14, "b": 2.0}
	case "calculator.divide":
		params = map[string]float64{"a": 100.0, "b": 3.0}
	case "utility.echo":
		params = map[string]string{"message": fmt.Sprintf("test message %d", id)}
	case "utility.ping":
		params = nil
	case "utility.getTime":
		params = nil
	case "utility.sleep":
		params = map[string]int{"seconds": 1}
	}

	return JSONRPCRequest{
		JSONRPC: "2.0",
		Method:  method,
		Params:  params,
		ID:      id,
	}
}

// PrintStats 打印统计信息
func PrintStats(stats *TestStats) {
	duration := stats.EndTime.Sub(stats.StartTime)
	
	fmt.Println("\n=== 性能测试结果 ===")
	fmt.Printf("测试持续时间: %v\n", duration)
	fmt.Printf("总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("总响应数: %d\n", stats.TotalResponses)
	fmt.Printf("成功请求: %d\n", stats.SuccessRequests)
	fmt.Printf("失败请求: %d\n", stats.FailedRequests)
	fmt.Printf("连接错误: %d\n", stats.ConnectionErrors)
	fmt.Printf("其他错误: %d\n", stats.TotalErrors)
	
	if stats.TotalRequests > 0 {
		fmt.Printf("请求成功率: %.2f%%\n", float64(stats.SuccessRequests)/float64(stats.TotalRequests)*100)
		fmt.Printf("平均QPS: %.2f\n", float64(stats.TotalRequests)/duration.Seconds())
	}

	// 响应时间统计
	if len(stats.ResponseTimes) > 0 {
		var total time.Duration
		min := stats.ResponseTimes[0]
		max := stats.ResponseTimes[0]

		for _, rt := range stats.ResponseTimes {
			total += rt
			if rt < min {
				min = rt
			}
			if rt > max {
				max = rt
			}
		}

		avg := total / time.Duration(len(stats.ResponseTimes))
		fmt.Printf("平均响应时间: %v\n", avg)
		fmt.Printf("最小响应时间: %v\n", min)
		fmt.Printf("最大响应时间: %v\n", max)
	}
}

func main() {
	var (
		serverURL   = flag.String("url", "ws://localhost:8080/ws", "WebSocket服务器地址")
		connections = flag.Int("connections", 10, "并发连接数")
		duration    = flag.Duration("duration", 30*time.Second, "测试持续时间")
		requestRate = flag.Int("rate", 10, "每秒请求数")
	)
	flag.Parse()

	config := TestConfig{
		ServerURL:   *serverURL,
		Connections: *connections,
		Duration:    *duration,
		RequestRate: *requestRate,
		TestMethods: []string{
			"calculator.add",
			"calculator.multiply",
			"utility.ping",
			"utility.echo",
			"utility.getTime",
		},
	}

	log.Printf("开始性能测试...")
	log.Printf("服务器地址: %s", config.ServerURL)
	log.Printf("并发连接数: %d", config.Connections)
	log.Printf("测试持续时间: %v", config.Duration)
	log.Printf("请求速率: %d req/s", config.RequestRate)

	stats := PerformanceTest(config)
	PrintStats(stats)
}
