# 基础WebSocket JSON-RPC性能测试
param(
    [string]$StatsUrl = "http://localhost:8080/stats"
)

Write-Host "=== WebSocket JSON-RPC 基础性能测试 ===" -ForegroundColor Green

# 检查服务器状态
Write-Host "检查服务器状态..." -ForegroundColor Yellow
try {
    $beforeStats = Invoke-RestMethod -Uri $StatsUrl -Method Get -TimeoutSec 5
    Write-Host "服务器运行正常" -ForegroundColor Green
    Write-Host "运行时间: $($beforeStats.uptime)"
    Write-Host "当前活跃连接: $($beforeStats.connections.active)"
    Write-Host "总连接数: $($beforeStats.connections.total)"
    Write-Host "总请求数: $($beforeStats.requests.total)"
    Write-Host "当前内存使用: $([math]::Round($beforeStats.memory.alloc / 1024 / 1024, 2))MB"
    Write-Host "堆内存使用: $([math]::Round($beforeStats.memory.heap_alloc / 1024 / 1024, 2))MB"
    Write-Host "Goroutine数量: $($beforeStats.memory.num_goroutine)"
    Write-Host "当前QPS: $($beforeStats.requests.qps.ToString('F2'))"
}
catch {
    Write-Host "错误: 无法连接到服务器" -ForegroundColor Red
    Write-Host "请确保服务器正在运行: go run main.go" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n请在另一个终端或浏览器中进行测试，然后按任意键查看更新后的统计信息..." -ForegroundColor Yellow
Read-Host

# 获取更新后的统计信息
Write-Host "`n获取更新后的服务器统计..." -ForegroundColor Yellow
try {
    $afterStats = Invoke-RestMethod -Uri $StatsUrl -Method Get -TimeoutSec 5
    
    Write-Host "`n=== 服务器性能统计 ===" -ForegroundColor Green
    Write-Host "运行时间: $($afterStats.uptime)"
    Write-Host "连接统计:"
    Write-Host "  总连接数: $($beforeStats.connections.total) -> $($afterStats.connections.total) (增加: $($afterStats.connections.total - $beforeStats.connections.total))"
    Write-Host "  活跃连接: $($afterStats.connections.active)"
    
    Write-Host "请求统计:"
    Write-Host "  总请求数: $($beforeStats.requests.total) -> $($afterStats.requests.total) (增加: $($afterStats.requests.total - $beforeStats.requests.total))"
    Write-Host "  成功响应: $($afterStats.requests.responses)"
    Write-Host "  错误数量: $($afterStats.requests.errors)"
    Write-Host "  平均响应时间: $($afterStats.requests.avg_time.ToString('F2'))ms"
    Write-Host "  当前QPS: $($afterStats.requests.qps.ToString('F2'))"
    
    Write-Host "内存统计:"
    Write-Host "  当前分配: $([math]::Round($beforeStats.memory.alloc / 1024 / 1024, 2))MB -> $([math]::Round($afterStats.memory.alloc / 1024 / 1024, 2))MB"
    Write-Host "  堆内存: $([math]::Round($beforeStats.memory.heap_alloc / 1024 / 1024, 2))MB -> $([math]::Round($afterStats.memory.heap_alloc / 1024 / 1024, 2))MB"
    Write-Host "  系统内存: $([math]::Round($afterStats.memory.sys / 1024 / 1024, 2))MB"
    Write-Host "  GC次数: $($afterStats.memory.num_gc)"
    
    Write-Host "系统统计:"
    Write-Host "  Goroutine: $($beforeStats.memory.num_goroutine) -> $($afterStats.memory.num_goroutine)"
    Write-Host "  CPU核心数: $($afterStats.cpu.num_cpu)"
    
    # 计算性能指标
    if ($afterStats.requests.total -gt $beforeStats.requests.total) {
        $newRequests = $afterStats.requests.total - $beforeStats.requests.total
        Write-Host "`n=== 性能指标 ===" -ForegroundColor Cyan
        Write-Host "新增请求数: $newRequests"
        
        if ($afterStats.requests.responses -gt 0) {
            $successRate = ($afterStats.requests.responses / $afterStats.requests.total) * 100
            Write-Host "总体成功率: $($successRate.ToString('F2'))%"
        }
        
        if ($afterStats.requests.avg_time -gt 0) {
            Write-Host "平均响应时间: $($afterStats.requests.avg_time.ToString('F2'))ms"
        }
    }
}
catch {
    Write-Host "获取统计信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试建议 ===" -ForegroundColor Yellow
Write-Host "1. 使用浏览器访问 http://localhost:8080 进行交互式测试"
Write-Host "2. 使用WebSocket客户端工具连接 ws://localhost:8080/ws"
Write-Host "3. 发送JSON-RPC请求，例如:"
Write-Host "   {`"jsonrpc`":`"2.0`",`"method`":`"calculator.add`",`"params`":{`"a`":10,`"b`":5},`"id`":1}"
Write-Host "4. 访问 http://localhost:8080/stats 查看实时统计"
Write-Host "5. 访问 http://localhost:8080/debug/pprof/ 进行性能分析"

Write-Host "`n测试完成！" -ForegroundColor Green
