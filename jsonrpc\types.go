package jsonrpc

import (
	"encoding/json"
	"fmt"
)

// JSON-RPC 2.0 协议版本
const Version = "2.0"

// Request 表示JSON-RPC 2.0请求
type Request struct {
	JSONRPC string          `json:"jsonrpc"`
	Method  string          `json:"method"`
	Params  json.RawMessage `json:"params,omitempty"`
	ID      interface{}     `json:"id,omitempty"`
}

// Response 表示JSON-RPC 2.0响应
type Response struct {
	JSONRPC string      `json:"jsonrpc"`
	Result  interface{} `json:"result,omitempty"`
	Error   *Error      `json:"error,omitempty"`
	ID      interface{} `json:"id"`
}

// Error 表示JSON-RPC 2.0错误
type Error struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 预定义的错误代码
const (
	ParseError     = -32700
	InvalidRequest = -32600
	MethodNotFound = -32601
	InvalidParams  = -32602
	InternalError  = -32603
)

// 预定义错误
var (
	ErrParse = &Error{
		Code:    ParseError,
		Message: "Parse error",
	}
	ErrInvalidRequest = &Error{
		Code:    InvalidRequest,
		Message: "Invalid Request",
	}
	ErrMethodNotFound = &Error{
		Code:    MethodNotFound,
		Message: "Method not found",
	}
	ErrInvalidParams = &Error{
		Code:    InvalidParams,
		Message: "Invalid params",
	}
	ErrInternal = &Error{
		Code:    InternalError,
		Message: "Internal error",
	}
)

// NewError 创建新的错误
func NewError(code int, message string, data interface{}) *Error {
	return &Error{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// Error 实现error接口
func (e *Error) Error() string {
	return fmt.Sprintf("JSON-RPC Error %d: %s", e.Code, e.Message)
}

// NewRequest 创建新的请求
func NewRequest(method string, params interface{}, id interface{}) (*Request, error) {
	var paramsBytes json.RawMessage
	if params != nil {
		bytes, err := json.Marshal(params)
		if err != nil {
			return nil, err
		}
		paramsBytes = bytes
	}

	return &Request{
		JSONRPC: Version,
		Method:  method,
		Params:  paramsBytes,
		ID:      id,
	}, nil
}

// NewResponse 创建成功响应
func NewResponse(result interface{}, id interface{}) *Response {
	return &Response{
		JSONRPC: Version,
		Result:  result,
		ID:      id,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(err *Error, id interface{}) *Response {
	return &Response{
		JSONRPC: Version,
		Error:   err,
		ID:      id,
	}
}

// IsNotification 检查请求是否为通知（没有ID）
func (r *Request) IsNotification() bool {
	return r.ID == nil
}

// Validate 验证请求格式
func (r *Request) Validate() error {
	if r.JSONRPC != Version {
		return ErrInvalidRequest
	}
	if r.Method == "" {
		return ErrInvalidRequest
	}
	return nil
}
