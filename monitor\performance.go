package monitor

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	mu                sync.RWMutex
	startTime         time.Time
	totalConnections  int64
	activeConnections int64
	totalRequests     int64
	totalResponses    int64
	totalErrors       int64
	requestTimes      []time.Duration
	memStats          []MemoryStats
	cpuStats          []CPUStats
	isRunning         bool
	stopChan          chan bool
}

// MemoryStats 内存统计
type MemoryStats struct {
	Timestamp    time.Time `json:"timestamp"`
	Alloc        uint64    `json:"alloc"`         // 当前分配的内存
	TotalAlloc   uint64    `json:"total_alloc"`   // 累计分配的内存
	Sys          uint64    `json:"sys"`           // 系统内存
	Lookups      uint64    `json:"lookups"`       // 指针查找次数
	Mallocs      uint64    `json:"mallocs"`       // 内存分配次数
	Frees        uint64    `json:"frees"`         // 内存释放次数
	HeapAlloc    uint64    `json:"heap_alloc"`    // 堆内存分配
	HeapSys      uint64    `json:"heap_sys"`      // 堆系统内存
	HeapIdle     uint64    `json:"heap_idle"`     // 堆空闲内存
	HeapInuse    uint64    `json:"heap_inuse"`    // 堆使用内存
	HeapReleased uint64    `json:"heap_released"` // 堆释放内存
	HeapObjects  uint64    `json:"heap_objects"`  // 堆对象数量
	StackInuse   uint64    `json:"stack_inuse"`   // 栈使用内存
	StackSys     uint64    `json:"stack_sys"`     // 栈系统内存
	NumGC        uint32    `json:"num_gc"`        // GC次数
	NumGoroutine int       `json:"num_goroutine"` // Goroutine数量
}

// CPUStats CPU统计
type CPUStats struct {
	Timestamp   time.Time `json:"timestamp"`
	NumCPU      int       `json:"num_cpu"`
	NumGoroutine int      `json:"num_goroutine"`
	CGOCalls    int64     `json:"cgo_calls"`
}

// ConnectionStats 连接统计
type ConnectionStats struct {
	Total  int64 `json:"total"`
	Active int64 `json:"active"`
}

// RequestStats 请求统计
type RequestStats struct {
	Total     int64   `json:"total"`
	Responses int64   `json:"responses"`
	Errors    int64   `json:"errors"`
	AvgTime   float64 `json:"avg_time_ms"`
	QPS       float64 `json:"qps"`
}

// PerformanceSnapshot 性能快照
type PerformanceSnapshot struct {
	Timestamp   time.Time       `json:"timestamp"`
	Uptime      string          `json:"uptime"`
	Memory      MemoryStats     `json:"memory"`
	CPU         CPUStats        `json:"cpu"`
	Connections ConnectionStats `json:"connections"`
	Requests    RequestStats    `json:"requests"`
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{
		startTime:    time.Now(),
		requestTimes: make([]time.Duration, 0),
		memStats:     make([]MemoryStats, 0),
		cpuStats:     make([]CPUStats, 0),
		stopChan:     make(chan bool),
	}
}

// Start 启动监控
func (pm *PerformanceMonitor) Start(ctx context.Context, interval time.Duration) {
	pm.mu.Lock()
	if pm.isRunning {
		pm.mu.Unlock()
		return
	}
	pm.isRunning = true
	pm.mu.Unlock()

	go pm.collectStats(ctx, interval)
	log.Println("性能监控已启动")
}

// Stop 停止监控
func (pm *PerformanceMonitor) Stop() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if !pm.isRunning {
		return
	}
	
	pm.isRunning = false
	close(pm.stopChan)
	log.Println("性能监控已停止")
}

// collectStats 收集统计信息
func (pm *PerformanceMonitor) collectStats(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.collectMemoryStats()
			pm.collectCPUStats()
		}
	}
}

// collectMemoryStats 收集内存统计
func (pm *PerformanceMonitor) collectMemoryStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	stats := MemoryStats{
		Timestamp:    time.Now(),
		Alloc:        m.Alloc,
		TotalAlloc:   m.TotalAlloc,
		Sys:          m.Sys,
		Lookups:      m.Lookups,
		Mallocs:      m.Mallocs,
		Frees:        m.Frees,
		HeapAlloc:    m.HeapAlloc,
		HeapSys:      m.HeapSys,
		HeapIdle:     m.HeapIdle,
		HeapInuse:    m.HeapInuse,
		HeapReleased: m.HeapReleased,
		HeapObjects:  m.HeapObjects,
		StackInuse:   m.StackInuse,
		StackSys:     m.StackSys,
		NumGC:        m.NumGC,
		NumGoroutine: runtime.NumGoroutine(),
	}

	pm.mu.Lock()
	pm.memStats = append(pm.memStats, stats)
	// 保持最近1000个记录
	if len(pm.memStats) > 1000 {
		pm.memStats = pm.memStats[1:]
	}
	pm.mu.Unlock()
}

// collectCPUStats 收集CPU统计
func (pm *PerformanceMonitor) collectCPUStats() {
	stats := CPUStats{
		Timestamp:    time.Now(),
		NumCPU:       runtime.NumCPU(),
		NumGoroutine: runtime.NumGoroutine(),
		CGOCalls:     runtime.NumCgoCall(),
	}

	pm.mu.Lock()
	pm.cpuStats = append(pm.cpuStats, stats)
	// 保持最近1000个记录
	if len(pm.cpuStats) > 1000 {
		pm.cpuStats = pm.cpuStats[1:]
	}
	pm.mu.Unlock()
}

// RecordConnection 记录连接
func (pm *PerformanceMonitor) RecordConnection(connected bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if connected {
		pm.totalConnections++
		pm.activeConnections++
	} else {
		pm.activeConnections--
		if pm.activeConnections < 0 {
			pm.activeConnections = 0
		}
	}
}

// RecordRequest 记录请求
func (pm *PerformanceMonitor) RecordRequest(duration time.Duration, success bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.totalRequests++
	if success {
		pm.totalResponses++
	} else {
		pm.totalErrors++
	}
	
	pm.requestTimes = append(pm.requestTimes, duration)
	// 保持最近10000个记录
	if len(pm.requestTimes) > 10000 {
		pm.requestTimes = pm.requestTimes[1:]
	}
}

// GetSnapshot 获取性能快照
func (pm *PerformanceMonitor) GetSnapshot() PerformanceSnapshot {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	now := time.Now()
	uptime := now.Sub(pm.startTime)

	// 计算平均响应时间
	var avgTime float64
	if len(pm.requestTimes) > 0 {
		var total time.Duration
		for _, t := range pm.requestTimes {
			total += t
		}
		avgTime = float64(total.Nanoseconds()) / float64(len(pm.requestTimes)) / 1e6 // 转换为毫秒
	}

	// 计算QPS
	var qps float64
	if uptime.Seconds() > 0 {
		qps = float64(pm.totalRequests) / uptime.Seconds()
	}

	// 获取最新的内存和CPU统计
	var memStats MemoryStats
	var cpuStats CPUStats
	
	if len(pm.memStats) > 0 {
		memStats = pm.memStats[len(pm.memStats)-1]
	}
	
	if len(pm.cpuStats) > 0 {
		cpuStats = pm.cpuStats[len(pm.cpuStats)-1]
	}

	return PerformanceSnapshot{
		Timestamp: now,
		Uptime:    uptime.String(),
		Memory:    memStats,
		CPU:       cpuStats,
		Connections: ConnectionStats{
			Total:  pm.totalConnections,
			Active: pm.activeConnections,
		},
		Requests: RequestStats{
			Total:     pm.totalRequests,
			Responses: pm.totalResponses,
			Errors:    pm.totalErrors,
			AvgTime:   avgTime,
			QPS:       qps,
		},
	}
}

// GetMemoryHistory 获取内存历史
func (pm *PerformanceMonitor) GetMemoryHistory() []MemoryStats {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	// 返回副本
	history := make([]MemoryStats, len(pm.memStats))
	copy(history, pm.memStats)
	return history
}

// PrintStats 打印统计信息
func (pm *PerformanceMonitor) PrintStats() {
	snapshot := pm.GetSnapshot()
	
	fmt.Println("\n=== 服务器性能统计 ===")
	fmt.Printf("运行时间: %s\n", snapshot.Uptime)
	fmt.Printf("连接统计: 总计=%d, 活跃=%d\n", snapshot.Connections.Total, snapshot.Connections.Active)
	fmt.Printf("请求统计: 总计=%d, 成功=%d, 错误=%d\n", 
		snapshot.Requests.Total, snapshot.Requests.Responses, snapshot.Requests.Errors)
	fmt.Printf("性能指标: QPS=%.2f, 平均响应时间=%.2fms\n", 
		snapshot.Requests.QPS, snapshot.Requests.AvgTime)
	
	fmt.Printf("内存使用: 当前=%.2fMB, 堆=%.2fMB, 系统=%.2fMB\n",
		float64(snapshot.Memory.Alloc)/1024/1024,
		float64(snapshot.Memory.HeapAlloc)/1024/1024,
		float64(snapshot.Memory.Sys)/1024/1024)
	
	fmt.Printf("Goroutine数量: %d\n", snapshot.Memory.NumGoroutine)
	fmt.Printf("GC次数: %d\n", snapshot.Memory.NumGC)
}

// ExportJSON 导出JSON格式的统计信息
func (pm *PerformanceMonitor) ExportJSON() ([]byte, error) {
	snapshot := pm.GetSnapshot()
	return json.MarshalIndent(snapshot, "", "  ")
}
