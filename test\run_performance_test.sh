#!/bin/bash

# WebSocket JSON-RPC 性能测试脚本
# 使用方法: ./run_performance_test.sh

set -e

echo "=== WebSocket JSON-RPC 性能测试 ==="

# 配置参数
SERVER_URL="ws://localhost:8080/ws"
TEST_DURATION="60s"
STATS_URL="http://localhost:8080/stats"

# 检查服务器是否运行
echo "检查服务器状态..."
if ! curl -s "$STATS_URL" > /dev/null; then
    echo "错误: 服务器未运行，请先启动服务器"
    echo "运行命令: go run main.go"
    exit 1
fi

echo "服务器运行正常"

# 创建结果目录
RESULT_DIR="performance_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RESULT_DIR"

echo "测试结果将保存到: $RESULT_DIR"

# 测试场景配置
declare -a TEST_SCENARIOS=(
    "10:5"      # 10个连接，每秒5个请求
    "50:10"     # 50个连接，每秒10个请求
    "100:20"    # 100个连接，每秒20个请求
    "200:50"    # 200个连接，每秒50个请求
    "500:100"   # 500个连接，每秒100个请求
    "1000:200"  # 1000个连接，每秒200个请求
)

# 编译测试程序
echo "编译性能测试程序..."
cd test
go build -o performance_test performance_test.go
cd ..

# 运行测试场景
for scenario in "${TEST_SCENARIOS[@]}"; do
    IFS=':' read -r connections rate <<< "$scenario"
    
    echo ""
    echo "=== 测试场景: ${connections}个连接, ${rate}req/s ==="
    
    # 获取测试前的服务器状态
    echo "获取测试前服务器状态..."
    curl -s "$STATS_URL" > "$RESULT_DIR/before_${connections}_${rate}.json"
    
    # 运行性能测试
    echo "开始性能测试..."
    ./test/performance_test \
        -url="$SERVER_URL" \
        -connections="$connections" \
        -duration="$TEST_DURATION" \
        -rate="$rate" \
        > "$RESULT_DIR/test_${connections}_${rate}.log" 2>&1 &
    
    TEST_PID=$!
    
    # 监控服务器性能
    echo "监控服务器性能..."
    MONITOR_LOG="$RESULT_DIR/monitor_${connections}_${rate}.log"
    
    # 启动性能监控
    {
        echo "时间,内存使用(MB),堆内存(MB),Goroutine数量,活跃连接,总请求,QPS"
        while kill -0 $TEST_PID 2>/dev/null; do
            TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
            STATS=$(curl -s "$STATS_URL" 2>/dev/null || echo '{}')
            
            # 解析JSON数据
            MEMORY_ALLOC=$(echo "$STATS" | jq -r '.memory.alloc // 0' 2>/dev/null || echo "0")
            HEAP_ALLOC=$(echo "$STATS" | jq -r '.memory.heap_alloc // 0' 2>/dev/null || echo "0")
            GOROUTINES=$(echo "$STATS" | jq -r '.memory.num_goroutine // 0' 2>/dev/null || echo "0")
            ACTIVE_CONN=$(echo "$STATS" | jq -r '.connections.active // 0' 2>/dev/null || echo "0")
            TOTAL_REQ=$(echo "$STATS" | jq -r '.requests.total // 0' 2>/dev/null || echo "0")
            QPS=$(echo "$STATS" | jq -r '.requests.qps // 0' 2>/dev/null || echo "0")
            
            # 转换内存单位为MB
            MEMORY_MB=$(echo "scale=2; $MEMORY_ALLOC / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
            HEAP_MB=$(echo "scale=2; $HEAP_ALLOC / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
            
            echo "$TIMESTAMP,$MEMORY_MB,$HEAP_MB,$GOROUTINES,$ACTIVE_CONN,$TOTAL_REQ,$QPS"
            
            sleep 2
        done
    } > "$MONITOR_LOG" &
    
    MONITOR_PID=$!
    
    # 等待测试完成
    wait $TEST_PID
    
    # 停止监控
    kill $MONITOR_PID 2>/dev/null || true
    
    # 获取测试后的服务器状态
    echo "获取测试后服务器状态..."
    sleep 2
    curl -s "$STATS_URL" > "$RESULT_DIR/after_${connections}_${rate}.json"
    
    echo "测试场景 ${connections}连接/${rate}req/s 完成"
    
    # 等待一段时间让服务器恢复
    echo "等待服务器恢复..."
    sleep 10
done

echo ""
echo "=== 所有测试完成 ==="

# 生成汇总报告
echo "生成测试报告..."
REPORT_FILE="$RESULT_DIR/performance_report.md"

cat > "$REPORT_FILE" << EOF
# WebSocket JSON-RPC 性能测试报告

测试时间: $(date)
测试持续时间: $TEST_DURATION
服务器地址: $SERVER_URL

## 测试场景

EOF

for scenario in "${TEST_SCENARIOS[@]}"; do
    IFS=':' read -r connections rate <<< "$scenario"
    
    echo "### ${connections}个连接, ${rate}req/s" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 提取测试结果
    if [ -f "$RESULT_DIR/test_${connections}_${rate}.log" ]; then
        echo "#### 客户端测试结果" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        tail -20 "$RESULT_DIR/test_${connections}_${rate}.log" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    # 服务器性能数据
    if [ -f "$RESULT_DIR/monitor_${connections}_${rate}.log" ]; then
        echo "#### 服务器性能监控" >> "$REPORT_FILE"
        echo "- 监控数据文件: monitor_${connections}_${rate}.log" >> "$REPORT_FILE"
        
        # 计算平均值
        if command -v awk >/dev/null 2>&1; then
            AVG_MEMORY=$(tail -n +2 "$RESULT_DIR/monitor_${connections}_${rate}.log" | awk -F',' '{sum+=$2; count++} END {if(count>0) printf "%.2f", sum/count; else print "0"}')
            AVG_GOROUTINES=$(tail -n +2 "$RESULT_DIR/monitor_${connections}_${rate}.log" | awk -F',' '{sum+=$4; count++} END {if(count>0) printf "%.0f", sum/count; else print "0"}')
            MAX_CONNECTIONS=$(tail -n +2 "$RESULT_DIR/monitor_${connections}_${rate}.log" | awk -F',' 'BEGIN{max=0} {if($5>max) max=$5} END {print max}')
            
            echo "- 平均内存使用: ${AVG_MEMORY}MB" >> "$REPORT_FILE"
            echo "- 平均Goroutine数量: $AVG_GOROUTINES" >> "$REPORT_FILE"
            echo "- 最大连接数: $MAX_CONNECTIONS" >> "$REPORT_FILE"
        fi
        echo "" >> "$REPORT_FILE"
    fi
done

echo "测试报告已生成: $REPORT_FILE"

# 打开结果目录
if command -v explorer >/dev/null 2>&1; then
    explorer "$RESULT_DIR"
elif command -v open >/dev/null 2>&1; then
    open "$RESULT_DIR"
fi

echo "测试完成！结果保存在: $RESULT_DIR"
