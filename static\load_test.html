<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 高负载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        label {
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .start-btn {
            background-color: #28a745;
            color: white;
        }
        .stop-btn {
            background-color: #dc3545;
            color: white;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.running {
            background-color: #d4edda;
            color: #155724;
        }
        .status.stopped {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>WebSocket 高负载测试工具</h1>
    
    <div class="container">
        <h3>测试配置</h3>
        <div class="controls">
            <div class="form-group">
                <label for="serverUrl">服务器地址:</label>
                <input type="text" id="serverUrl" value="ws://localhost:8080/ws">
            </div>
            <div class="form-group">
                <label for="connections">连接数:</label>
                <input type="number" id="connections" value="100" min="1" max="2000">
            </div>
            <div class="form-group">
                <label for="requestRate">请求速率 (req/s/conn):</label>
                <input type="number" id="requestRate" value="2" min="1" max="10">
            </div>
            <div class="form-group">
                <label for="duration">持续时间 (秒):</label>
                <input type="number" id="duration" value="60" min="10" max="300">
            </div>
            <div class="form-group">
                <label for="batchSize">批次大小:</label>
                <input type="number" id="batchSize" value="50" min="10" max="100">
            </div>
            <div class="form-group">
                <label for="method">测试方法:</label>
                <select id="method">
                    <option value="calculator.add">calculator.add</option>
                    <option value="calculator.multiply">calculator.multiply</option>
                    <option value="utility.ping">utility.ping</option>
                    <option value="utility.echo">utility.echo</option>
                </select>
            </div>
        </div>
        
        <div>
            <button class="start-btn" onclick="startTest()">开始测试</button>
            <button class="stop-btn" onclick="stopTest()">停止测试</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status stopped">测试未开始</div>
        
        <div class="progress">
            <div id="progressBar" class="progress-bar" style="width: 0%"></div>
        </div>
    </div>

    <div class="container">
        <h3>实时统计</h3>
        <div class="stats">
            <div class="stat-card">
                <div id="connectedCount" class="stat-value">0</div>
                <div class="stat-label">已连接</div>
            </div>
            <div class="stat-card">
                <div id="totalRequests" class="stat-value">0</div>
                <div class="stat-label">总请求</div>
            </div>
            <div class="stat-card">
                <div id="totalResponses" class="stat-value">0</div>
                <div class="stat-label">总响应</div>
            </div>
            <div class="stat-card">
                <div id="totalErrors" class="stat-value">0</div>
                <div class="stat-label">错误数</div>
            </div>
            <div class="stat-card">
                <div id="currentQPS" class="stat-value">0</div>
                <div class="stat-label">当前QPS</div>
            </div>
            <div class="stat-card">
                <div id="successRate" class="stat-value">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>测试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let testRunning = false;
        let connections = [];
        let stats = {
            connected: 0,
            totalRequests: 0,
            totalResponses: 0,
            totalErrors: 0,
            startTime: null
        };
        let testTimer = null;
        let statsTimer = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStats() {
            document.getElementById('connectedCount').textContent = stats.connected;
            document.getElementById('totalRequests').textContent = stats.totalRequests;
            document.getElementById('totalResponses').textContent = stats.totalResponses;
            document.getElementById('totalErrors').textContent = stats.totalErrors;
            
            if (stats.startTime) {
                const elapsed = (Date.now() - stats.startTime) / 1000;
                const qps = elapsed > 0 ? (stats.totalRequests / elapsed).toFixed(2) : 0;
                document.getElementById('currentQPS').textContent = qps;
            }
            
            const successRate = stats.totalRequests > 0 ? 
                ((stats.totalResponses / stats.totalRequests) * 100).toFixed(1) + '%' : '0%';
            document.getElementById('successRate').textContent = successRate;
        }

        function createConnection(id, serverUrl, method, requestRate) {
            return new Promise((resolve) => {
                const ws = new WebSocket(serverUrl);
                let requestId = 1;
                let requestInterval = null;
                
                ws.onopen = () => {
                    stats.connected++;
                    log(`连接 ${id} 已建立`);
                    
                    // 开始发送请求
                    requestInterval = setInterval(() => {
                        if (!testRunning) {
                            clearInterval(requestInterval);
                            return;
                        }
                        
                        const request = createRequest(method, requestId++);
                        try {
                            ws.send(JSON.stringify(request));
                            stats.totalRequests++;
                        } catch (error) {
                            stats.totalErrors++;
                            log(`连接 ${id} 发送请求失败: ${error.message}`, 'error');
                        }
                    }, 1000 / requestRate);
                    
                    resolve(ws);
                };
                
                ws.onmessage = (event) => {
                    stats.totalResponses++;
                    try {
                        const response = JSON.parse(event.data);
                        if (response.error) {
                            stats.totalErrors++;
                        }
                    } catch (error) {
                        stats.totalErrors++;
                    }
                };
                
                ws.onerror = (error) => {
                    stats.totalErrors++;
                    log(`连接 ${id} 错误: ${error.message}`, 'error');
                };
                
                ws.onclose = () => {
                    stats.connected--;
                    if (requestInterval) {
                        clearInterval(requestInterval);
                    }
                    log(`连接 ${id} 已关闭`);
                };
                
                // 保存清理函数
                ws.cleanup = () => {
                    if (requestInterval) {
                        clearInterval(requestInterval);
                    }
                    ws.close();
                };
            });
        }

        function createRequest(method, id) {
            let params = null;
            
            switch (method) {
                case 'calculator.add':
                    params = { a: Math.random() * 100, b: Math.random() * 100 };
                    break;
                case 'calculator.multiply':
                    params = { a: Math.random() * 10, b: Math.random() * 10 };
                    break;
                case 'utility.echo':
                    params = { message: `Test message ${id}` };
                    break;
                case 'utility.ping':
                    params = null;
                    break;
            }
            
            return {
                jsonrpc: '2.0',
                method: method,
                params: params,
                id: id
            };
        }

        async function startTest() {
            if (testRunning) return;
            
            testRunning = true;
            connections = [];
            stats = {
                connected: 0,
                totalRequests: 0,
                totalResponses: 0,
                totalErrors: 0,
                startTime: Date.now()
            };
            
            const serverUrl = document.getElementById('serverUrl').value;
            const connectionCount = parseInt(document.getElementById('connections').value);
            const requestRate = parseInt(document.getElementById('requestRate').value);
            const duration = parseInt(document.getElementById('duration').value);
            const batchSize = parseInt(document.getElementById('batchSize').value);
            const method = document.getElementById('method').value;
            
            document.getElementById('status').textContent = '测试运行中...';
            document.getElementById('status').className = 'status running';
            
            log(`开始高负载测试: ${connectionCount}个连接, ${requestRate}req/s/conn, 持续${duration}秒`);
            log(`使用方法: ${method}`);
            
            // 分批创建连接
            for (let i = 0; i < connectionCount; i += batchSize) {
                if (!testRunning) break;
                
                const end = Math.min(i + batchSize, connectionCount);
                log(`创建连接 ${i + 1} 到 ${end}...`);
                
                const batch = [];
                for (let j = i; j < end; j++) {
                    batch.push(createConnection(j + 1, serverUrl, method, requestRate));
                }
                
                try {
                    const batchConnections = await Promise.all(batch);
                    connections.push(...batchConnections);
                } catch (error) {
                    log(`批次创建失败: ${error.message}`, 'error');
                }
                
                // 更新进度
                const progress = (end / connectionCount) * 100;
                document.getElementById('progressBar').style.width = progress + '%';
                
                // 批次间延迟
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            log(`连接创建完成，成功: ${stats.connected}, 目标: ${connectionCount}`);
            
            // 启动统计更新
            statsTimer = setInterval(updateStats, 1000);
            
            // 设置测试结束定时器
            testTimer = setTimeout(() => {
                stopTest();
            }, duration * 1000);
        }

        function stopTest() {
            if (!testRunning) return;
            
            testRunning = false;
            
            if (testTimer) {
                clearTimeout(testTimer);
                testTimer = null;
            }
            
            if (statsTimer) {
                clearInterval(statsTimer);
                statsTimer = null;
            }
            
            // 关闭所有连接
            connections.forEach(ws => {
                if (ws && ws.cleanup) {
                    ws.cleanup();
                }
            });
            connections = [];
            
            document.getElementById('status').textContent = '测试已停止';
            document.getElementById('status').className = 'status stopped';
            document.getElementById('progressBar').style.width = '0%';
            
            // 最终统计
            const duration = (Date.now() - stats.startTime) / 1000;
            const avgQPS = duration > 0 ? (stats.totalRequests / duration).toFixed(2) : 0;
            const successRate = stats.totalRequests > 0 ? 
                ((stats.totalResponses / stats.totalRequests) * 100).toFixed(1) : 0;
            
            log('=== 测试完成 ===', 'success');
            log(`测试时长: ${duration.toFixed(1)}秒`);
            log(`总请求: ${stats.totalRequests}`);
            log(`总响应: ${stats.totalResponses}`);
            log(`错误数: ${stats.totalErrors}`);
            log(`平均QPS: ${avgQPS}`);
            log(`成功率: ${successRate}%`);
            
            updateStats();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面关闭时清理
        window.addEventListener('beforeunload', () => {
            stopTest();
        });
    </script>
</body>
</html>
