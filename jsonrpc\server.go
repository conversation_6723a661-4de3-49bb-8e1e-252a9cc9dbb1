package jsonrpc

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"sync"
)

// Handler 定义RPC方法处理函数类型
type Handler func(ctx context.Context, params json.RawMessage) (interface{}, error)

// Server RPC服务器
type Server struct {
	mu       sync.RWMutex
	handlers map[string]Handler
}

// NewServer 创建新的RPC服务器
func NewServer() *Server {
	return &Server{
		handlers: make(map[string]Handler),
	}
}

// Register 注册RPC方法
func (s *Server) Register(method string, handler Handler) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.handlers[method] = handler
}

// RegisterFunc 注册函数作为RPC方法
// 函数签名应该是: func(ctx context.Context, params ParamsType) (ResultType, error)
func (s *Server) RegisterFunc(method string, fn interface{}) error {
	handler, err := s.makeHandler(fn)
	if err != nil {
		return err
	}
	s.Register(method, handler)
	return nil
}

// makeHandler 将普通函数转换为Handler
func (s *Server) makeHandler(fn interface{}) (Handler, error) {
	fnValue := reflect.ValueOf(fn)
	fnType := fnValue.Type()

	// 检查函数签名
	if fnType.Kind() != reflect.Func {
		return nil, fmt.Errorf("handler must be a function")
	}

	if fnType.NumIn() < 1 || fnType.NumIn() > 2 {
		return nil, fmt.Errorf("handler must have 1 or 2 parameters")
	}

	if fnType.NumOut() != 2 {
		return nil, fmt.Errorf("handler must return exactly 2 values")
	}

	// 第一个参数必须是context.Context
	if !fnType.In(0).Implements(reflect.TypeOf((*context.Context)(nil)).Elem()) {
		return nil, fmt.Errorf("first parameter must be context.Context")
	}

	// 最后一个返回值必须是error
	if !fnType.Out(1).Implements(reflect.TypeOf((*error)(nil)).Elem()) {
		return nil, fmt.Errorf("last return value must be error")
	}

	return func(ctx context.Context, params json.RawMessage) (interface{}, error) {
		args := []reflect.Value{reflect.ValueOf(ctx)}

		// 如果有参数，解析参数
		if fnType.NumIn() == 2 {
			paramType := fnType.In(1)
			paramValue := reflect.New(paramType).Interface()

			if len(params) > 0 {
				if err := json.Unmarshal(params, paramValue); err != nil {
					return nil, fmt.Errorf("failed to unmarshal params: %w", err)
				}
			}

			args = append(args, reflect.ValueOf(paramValue).Elem())
		}

		// 调用函数
		results := fnValue.Call(args)

		// 处理返回值
		var result interface{}
		var err error

		if !results[0].IsNil() {
			result = results[0].Interface()
		}

		if !results[1].IsNil() {
			err = results[1].Interface().(error)
		}

		return result, err
	}, nil
}

// Handle 处理JSON-RPC请求
func (s *Server) Handle(ctx context.Context, req *Request) *Response {
	// 验证请求
	if err := req.Validate(); err != nil {
		return NewErrorResponse(err.(*Error), req.ID)
	}

	// 查找处理函数
	s.mu.RLock()
	handler, exists := s.handlers[req.Method]
	s.mu.RUnlock()

	if !exists {
		return NewErrorResponse(ErrMethodNotFound, req.ID)
	}

	// 调用处理函数
	result, err := handler(ctx, req.Params)
	if err != nil {
		// 如果是JSON-RPC错误，直接返回
		if rpcErr, ok := err.(*Error); ok {
			return NewErrorResponse(rpcErr, req.ID)
		}
		// 否则包装为内部错误
		rpcErr := NewError(InternalError, err.Error(), nil)
		return NewErrorResponse(rpcErr, req.ID)
	}

	return NewResponse(result, req.ID)
}

// HandleBatch 处理批量请求
func (s *Server) HandleBatch(ctx context.Context, requests []*Request) []*Response {
	responses := make([]*Response, 0, len(requests))

	for _, req := range requests {
		resp := s.Handle(ctx, req)
		// 通知请求不返回响应
		if !req.IsNotification() {
			responses = append(responses, resp)
		}
	}

	return responses
}

// ListMethods 列出所有注册的方法
func (s *Server) ListMethods() []string {
	s.mu.RLock()
	defer s.mu.RUnlock()

	methods := make([]string, 0, len(s.handlers))
	for method := range s.handlers {
		methods = append(methods, method)
	}
	return methods
}
