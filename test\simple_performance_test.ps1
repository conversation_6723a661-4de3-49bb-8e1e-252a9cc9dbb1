# WebSocket JSON-RPC 简单性能测试脚本
param(
    [int]$Connections = 10,
    [int]$RequestsPerConnection = 50,
    [string]$ServerUrl = "ws://localhost:8080/ws",
    [string]$StatsUrl = "http://localhost:8080/stats"
)

Write-Host "=== WebSocket JSON-RPC 简单性能测试 ===" -ForegroundColor Green
Write-Host "服务器地址: $ServerUrl"
Write-Host "统计地址: $StatsUrl"
Write-Host "连接数: $Connections"
Write-Host "每连接请求数: $RequestsPerConnection"
Write-Host "总请求数: $($Connections * $RequestsPerConnection)"

# 检查服务器状态
Write-Host "`n检查服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri $StatsUrl -Method Get -TimeoutSec 5
    Write-Host "服务器运行正常" -ForegroundColor Green
    Write-Host "当前活跃连接: $($response.connections.active)"
    Write-Host "当前内存使用: $([math]::Round($response.memory.alloc / 1024 / 1024, 2))MB"
}
catch {
    Write-Host "错误: 无法连接到服务器，请确保服务器正在运行" -ForegroundColor Red
    Write-Host "启动命令: go run main.go" -ForegroundColor Yellow
    exit 1
}

# 获取测试前状态
Write-Host "`n获取测试前服务器状态..." -ForegroundColor Yellow
$beforeStats = Invoke-RestMethod -Uri $StatsUrl -Method Get

# 使用JavaScript创建WebSocket测试
$jsScript = @"
const WebSocket = require('ws');

const serverUrl = '$ServerUrl';
const connections = $Connections;
const requestsPerConnection = $RequestsPerConnection;

let totalConnections = 0;
let totalRequests = 0;
let totalResponses = 0;
let totalErrors = 0;
let startTime = Date.now();

console.log('开始创建连接...');

for (let i = 0; i < connections; i++) {
    setTimeout(() => {
        createConnection(i);
    }, i * 10); // 每10ms创建一个连接
}

function createConnection(connId) {
    const ws = new WebSocket(serverUrl);
    
    ws.on('open', () => {
        totalConnections++;
        console.log(`连接 ${connId} 已建立`);
        
        // 发送请求
        for (let j = 0; j < requestsPerConnection; j++) {
            setTimeout(() => {
                sendRequest(ws, j + 1);
            }, j * 50); // 每50ms发送一个请求
        }
    });
    
    ws.on('message', (data) => {
        totalResponses++;
        const response = JSON.parse(data);
        if (response.error) {
            totalErrors++;
        }
    });
    
    ws.on('error', (error) => {
        console.error(`连接 ${connId} 错误:`, error.message);
        totalErrors++;
    });
    
    ws.on('close', () => {
        console.log(`连接 ${connId} 已关闭`);
    });
}

function sendRequest(ws, requestId) {
    const request = {
        jsonrpc: '2.0',
        method: 'calculator.add',
        params: { a: 10.5, b: 5.3 },
        id: requestId
    };
    
    try {
        ws.send(JSON.stringify(request));
        totalRequests++;
    } catch (error) {
        totalErrors++;
        console.error('发送请求失败:', error.message);
    }
}

// 等待测试完成
setTimeout(() => {
    const duration = (Date.now() - startTime) / 1000;
    
    console.log('\n=== 测试结果 ===');
    console.log(`测试时间: ${duration.toFixed(2)}秒`);
    console.log(`成功连接: ${totalConnections}`);
    console.log(`发送请求: ${totalRequests}`);
    console.log(`收到响应: ${totalResponses}`);
    console.log(`错误数量: ${totalErrors}`);
    
    if (totalRequests > 0) {
        const successRate = (totalResponses / totalRequests * 100).toFixed(2);
        console.log(`成功率: ${successRate}%`);
    }
    
    if (duration > 0) {
        const qps = (totalRequests / duration).toFixed(2);
        console.log(`QPS: ${qps}`);
    }
    
    process.exit(0);
}, 30000); // 30秒后结束测试
"@

# 将JavaScript保存到临时文件
$tempJsFile = [System.IO.Path]::GetTempFileName() + ".js"
$jsScript | Out-File -FilePath $tempJsFile -Encoding UTF8

Write-Host "`n开始性能测试..." -ForegroundColor Yellow

# 检查是否安装了Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "使用Node.js进行测试: $nodeVersion"

        # 安装ws模块（如果需要）
        $wsInstalled = npm list ws --depth=0 2>$null
        if (-not $wsInstalled) {
            Write-Host "安装WebSocket模块..."
            npm install ws --no-save 2>$null
        }

        # 运行测试
        node $tempJsFile
    }
}
catch {
    Write-Host "Node.js未安装，使用PowerShell WebSocket测试..." -ForegroundColor Yellow
    
    # PowerShell WebSocket测试（简化版）
    Add-Type -AssemblyName System.Net.WebSockets
    Add-Type -AssemblyName System.Threading.Tasks
    
    $startTime = Get-Date
    $totalRequests = 0
    $totalErrors = 0
    
    Write-Host "创建 $Connections 个连接进行测试..."
    
    for ($i = 0; $i -lt $Connections; $i++) {
        try {
            # 这里是简化的测试，实际WebSocket连接需要更复杂的实现
            $totalRequests += $RequestsPerConnection
            Start-Sleep -Milliseconds 100
        }
        catch {
            $totalErrors++
        }
    }
    
    $duration = (Get-Date) - $startTime
    Write-Host "`n=== 简化测试结果 ===" -ForegroundColor Green
    Write-Host "测试时间: $($duration.TotalSeconds.ToString('F2'))秒"
    Write-Host "模拟请求: $totalRequests"
    Write-Host "错误数量: $totalErrors"
    
    if ($duration.TotalSeconds -gt 0) {
        $qps = $totalRequests / $duration.TotalSeconds
        Write-Host "模拟QPS: $($qps.ToString('F2'))"
    }
}

# 清理临时文件
if (Test-Path $tempJsFile) {
    Remove-Item $tempJsFile -Force
}

# 等待一段时间让服务器处理完成
Start-Sleep -Seconds 3

# 获取测试后状态
Write-Host "`n获取测试后服务器状态..." -ForegroundColor Yellow
$afterStats = Invoke-RestMethod -Uri $StatsUrl -Method Get

# 比较前后状态
Write-Host "`n=== 服务器性能对比 ===" -ForegroundColor Green
Write-Host "测试前后对比:"
Write-Host "  总连接数: $($beforeStats.connections.total) -> $($afterStats.connections.total) (增加: $($afterStats.connections.total - $beforeStats.connections.total))"
Write-Host "  活跃连接: $($beforeStats.connections.active) -> $($afterStats.connections.active)"
Write-Host "  总请求数: $($beforeStats.requests.total) -> $($afterStats.requests.total) (增加: $($afterStats.requests.total - $beforeStats.requests.total))"
Write-Host "  内存使用: $([math]::Round($beforeStats.memory.alloc / 1024 / 1024, 2))MB -> $([math]::Round($afterStats.memory.alloc / 1024 / 1024, 2))MB"
Write-Host "  Goroutine: $($beforeStats.memory.num_goroutine) -> $($afterStats.memory.num_goroutine)"
Write-Host "  当前QPS: $($afterStats.requests.qps.ToString('F2'))"

Write-Host "`n测试完成！" -ForegroundColor Green
