package websocket

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"golang-ws-jsonrpc/jsonrpc"

	"github.com/gorilla/websocket"
)

// 配置WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源（生产环境中应该更严格）
		return true
	},
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

// Client 表示WebSocket客户端连接
type Client struct {
	conn   *websocket.Conn
	send   chan []byte
	server *Server
	id     string
}

// PerformanceMonitor 性能监控接口
type PerformanceMonitor interface {
	RecordConnection(connected bool)
	RecordRequest(duration time.Duration, success bool)
}

// Server WebSocket服务器
type Server struct {
	rpcServer   *jsonrpc.Server
	clients     map[*Client]bool
	register    chan *Client
	unregister  chan *Client
	mu          sync.RWMutex
	perfMonitor PerformanceMonitor
}

// NewServer 创建新的WebSocket服务器
func NewServer(rpcServer *jsonrpc.Server) *Server {
	return &Server{
		rpcServer:  rpcServer,
		clients:    make(map[*Client]bool),
		register:   make(chan *Client),
		unregister: make(chan *Client),
	}
}

// SetPerformanceMonitor 设置性能监控器
func (s *Server) SetPerformanceMonitor(monitor PerformanceMonitor) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.perfMonitor = monitor
}

// Run 启动WebSocket服务器
func (s *Server) Run() {
	for {
		select {
		case client := <-s.register:
			s.mu.Lock()
			s.clients[client] = true
			if s.perfMonitor != nil {
				s.perfMonitor.RecordConnection(true)
			}
			s.mu.Unlock()
			log.Printf("Client %s connected", client.id)

		case client := <-s.unregister:
			s.mu.Lock()
			if _, ok := s.clients[client]; ok {
				delete(s.clients, client)
				close(client.send)
				if s.perfMonitor != nil {
					s.perfMonitor.RecordConnection(false)
				}
				s.mu.Unlock()
				log.Printf("Client %s disconnected", client.id)
			} else {
				s.mu.Unlock()
			}
		}
	}
}

// HandleWebSocket 处理WebSocket连接
func (s *Server) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	client := &Client{
		conn:   conn,
		send:   make(chan []byte, 256),
		server: s,
		id:     generateClientID(),
	}

	s.register <- client

	// 启动客户端的读写协程
	go client.writePump()
	go client.readPump()
}

// readPump 处理从WebSocket读取消息
func (c *Client) readPump() {
	defer func() {
		c.server.unregister <- c
		c.conn.Close()
	}()

	// 设置读取超时
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// 处理JSON-RPC消息
		c.handleMessage(message)
	}
}

// writePump 处理向WebSocket写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理接收到的消息
func (c *Client) handleMessage(message []byte) {
	ctx := context.Background()
	startTime := time.Now()

	// 尝试解析为单个请求
	var req jsonrpc.Request
	if err := json.Unmarshal(message, &req); err == nil {
		// 单个请求
		resp := c.server.rpcServer.Handle(ctx, &req)
		duration := time.Since(startTime)

		// 记录性能统计
		if c.server.perfMonitor != nil {
			success := resp.Error == nil
			c.server.perfMonitor.RecordRequest(duration, success)
		}

		if !req.IsNotification() {
			c.sendResponse(resp)
		}
		return
	}

	// 尝试解析为批量请求
	var requests []*jsonrpc.Request
	if err := json.Unmarshal(message, &requests); err == nil {
		// 批量请求
		responses := c.server.rpcServer.HandleBatch(ctx, requests)
		if len(responses) > 0 {
			c.sendBatchResponse(responses)
		}
		return
	}

	// 解析失败，返回解析错误
	resp := jsonrpc.NewErrorResponse(jsonrpc.ErrParse, nil)
	c.sendResponse(resp)
}

// sendResponse 发送单个响应
func (c *Client) sendResponse(resp *jsonrpc.Response) {
	data, err := json.Marshal(resp)
	if err != nil {
		log.Printf("Failed to marshal response: %v", err)
		return
	}

	select {
	case c.send <- data:
	default:
		close(c.send)
	}
}

// sendBatchResponse 发送批量响应
func (c *Client) sendBatchResponse(responses []*jsonrpc.Response) {
	data, err := json.Marshal(responses)
	if err != nil {
		log.Printf("Failed to marshal batch response: %v", err)
		return
	}

	select {
	case c.send <- data:
	default:
		close(c.send)
	}
}

// generateClientID 生成客户端ID
func generateClientID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(6)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
