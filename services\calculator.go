package services

import (
	"context"
	"fmt"
	"time"
)

// CalculatorService 计算器服务
type CalculatorService struct{}

// NewCalculatorService 创建计算器服务
func NewCalculatorService() *CalculatorService {
	return &CalculatorService{}
}

// AddParams 加法参数
type AddParams struct {
	A float64 `json:"a"`
	B float64 `json:"b"`
}

// AddResult 加法结果
type AddResult struct {
	Result float64 `json:"result"`
}

// Add 加法运算
func (c *CalculatorService) Add(ctx context.Context, params AddParams) (*AddResult, error) {
	result := params.A + params.B
	return &AddResult{Result: result}, nil
}

// SubtractParams 减法参数
type SubtractParams struct {
	A float64 `json:"a"`
	B float64 `json:"b"`
}

// SubtractResult 减法结果
type SubtractResult struct {
	Result float64 `json:"result"`
}

// Subtract 减法运算
func (c *CalculatorService) Subtract(ctx context.Context, params SubtractParams) (*SubtractResult, error) {
	result := params.A - params.B
	return &SubtractResult{Result: result}, nil
}

// MultiplyParams 乘法参数
type MultiplyParams struct {
	A float64 `json:"a"`
	B float64 `json:"b"`
}

// MultiplyResult 乘法结果
type MultiplyResult struct {
	Result float64 `json:"result"`
}

// Multiply 乘法运算
func (c *CalculatorService) Multiply(ctx context.Context, params MultiplyParams) (*MultiplyResult, error) {
	result := params.A * params.B
	return &MultiplyResult{Result: result}, nil
}

// DivideParams 除法参数
type DivideParams struct {
	A float64 `json:"a"`
	B float64 `json:"b"`
}

// DivideResult 除法结果
type DivideResult struct {
	Result float64 `json:"result"`
}

// Divide 除法运算
func (c *CalculatorService) Divide(ctx context.Context, params DivideParams) (*DivideResult, error) {
	if params.B == 0 {
		return nil, fmt.Errorf("division by zero")
	}
	result := params.A / params.B
	return &DivideResult{Result: result}, nil
}

// UtilityService 工具服务
type UtilityService struct{}

// NewUtilityService 创建工具服务
func NewUtilityService() *UtilityService {
	return &UtilityService{}
}

// PingResult Ping结果
type PingResult struct {
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// Ping 测试连接
func (u *UtilityService) Ping(ctx context.Context) (*PingResult, error) {
	return &PingResult{
		Message:   "pong",
		Timestamp: time.Now(),
	}, nil
}

// EchoParams Echo参数
type EchoParams struct {
	Message string `json:"message"`
}

// EchoResult Echo结果
type EchoResult struct {
	Echo      string    `json:"echo"`
	Timestamp time.Time `json:"timestamp"`
}

// Echo 回显消息
func (u *UtilityService) Echo(ctx context.Context, params EchoParams) (*EchoResult, error) {
	return &EchoResult{
		Echo:      params.Message,
		Timestamp: time.Now(),
	}, nil
}

// GetTimeResult 获取时间结果
type GetTimeResult struct {
	CurrentTime time.Time `json:"current_time"`
	Timezone    string    `json:"timezone"`
}

// GetTime 获取当前时间
func (u *UtilityService) GetTime(ctx context.Context) (*GetTimeResult, error) {
	now := time.Now()
	return &GetTimeResult{
		CurrentTime: now,
		Timezone:    now.Location().String(),
	}, nil
}

// SleepParams 睡眠参数
type SleepParams struct {
	Seconds int `json:"seconds"`
}

// SleepResult 睡眠结果
type SleepResult struct {
	Message string `json:"message"`
	Slept   int    `json:"slept_seconds"`
}

// Sleep 模拟长时间运行的操作
func (u *UtilityService) Sleep(ctx context.Context, params SleepParams) (*SleepResult, error) {
	if params.Seconds < 0 || params.Seconds > 10 {
		return nil, fmt.Errorf("sleep duration must be between 0 and 10 seconds")
	}

	select {
	case <-time.After(time.Duration(params.Seconds) * time.Second):
		return &SleepResult{
			Message: "Sleep completed",
			Slept:   params.Seconds,
		}, nil
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}
