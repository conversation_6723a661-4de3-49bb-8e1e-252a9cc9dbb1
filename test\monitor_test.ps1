# 简单的服务器监控测试
param(
    [string]$StatsUrl = "http://localhost:8080/stats"
)

Write-Host "=== WebSocket JSON-RPC 服务器监控 ===" -ForegroundColor Green

function Get-ServerStats {
    try {
        $stats = Invoke-RestMethod -Uri $StatsUrl -Method Get -TimeoutSec 5
        return $stats
    }
    catch {
        Write-Host "无法获取服务器统计信息: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Show-Stats {
    param($stats, $title)
    
    if ($stats -eq $null) {
        return
    }
    
    Write-Host "`n=== $title ===" -ForegroundColor Cyan
    Write-Host "运行时间: $($stats.uptime)"
    Write-Host "连接统计:"
    Write-Host "  总连接数: $($stats.connections.total)"
    Write-Host "  活跃连接: $($stats.connections.active)"
    
    Write-Host "请求统计:"
    Write-Host "  总请求数: $($stats.requests.total)"
    Write-Host "  成功响应: $($stats.requests.responses)"
    Write-Host "  错误数量: $($stats.requests.errors)"
    Write-Host "  平均响应时间: $($stats.requests.avg_time)ms"
    Write-Host "  当前QPS: $($stats.requests.qps)"
    
    Write-Host "内存统计:"
    $allocMB = [math]::Round($stats.memory.alloc / 1024 / 1024, 2)
    $heapMB = [math]::Round($stats.memory.heap_alloc / 1024 / 1024, 2)
    $sysMB = [math]::Round($stats.memory.sys / 1024 / 1024, 2)
    Write-Host "  当前分配: ${allocMB}MB"
    Write-Host "  堆内存: ${heapMB}MB"
    Write-Host "  系统内存: ${sysMB}MB"
    Write-Host "  GC次数: $($stats.memory.num_gc)"
    Write-Host "  Goroutine数量: $($stats.memory.num_goroutine)"
}

# 获取初始状态
$beforeStats = Get-ServerStats
if ($beforeStats -eq $null) {
    Write-Host "服务器未运行，请先启动服务器: go run main.go" -ForegroundColor Red
    exit 1
}

Show-Stats $beforeStats "服务器当前状态"

Write-Host "`n现在可以进行测试了！" -ForegroundColor Yellow
Write-Host "测试方法:"
Write-Host "1. 打开浏览器访问: http://localhost:8080"
Write-Host "2. 使用WebSocket客户端连接: ws://localhost:8080/ws"
Write-Host "3. 发送JSON-RPC请求进行测试"
Write-Host "`n按Enter键查看更新后的统计信息..."

Read-Host

# 获取更新后的状态
$afterStats = Get-ServerStats
if ($afterStats -ne $null) {
    Show-Stats $afterStats "测试后服务器状态"
    
    # 计算差异
    Write-Host "`n=== 变化统计 ===" -ForegroundColor Green
    $connDiff = $afterStats.connections.total - $beforeStats.connections.total
    $reqDiff = $afterStats.requests.total - $beforeStats.requests.total
    $memDiff = [math]::Round(($afterStats.memory.alloc - $beforeStats.memory.alloc) / 1024 / 1024, 2)
    $goroutineDiff = $afterStats.memory.num_goroutine - $beforeStats.memory.num_goroutine
    
    Write-Host "连接增加: $connDiff"
    Write-Host "请求增加: $reqDiff"
    Write-Host "内存变化: ${memDiff}MB"
    Write-Host "Goroutine变化: $goroutineDiff"
}

Write-Host "`n=== 性能分析工具 ===" -ForegroundColor Yellow
Write-Host "访问以下URL进行详细性能分析:"
Write-Host "- 实时统计: http://localhost:8080/stats"
Write-Host "- CPU分析: http://localhost:8080/debug/pprof/profile"
Write-Host "- 内存分析: http://localhost:8080/debug/pprof/heap"
Write-Host "- Goroutine分析: http://localhost:8080/debug/pprof/goroutine"
Write-Host "- 阻塞分析: http://localhost:8080/debug/pprof/block"

Write-Host "`n测试完成！" -ForegroundColor Green
